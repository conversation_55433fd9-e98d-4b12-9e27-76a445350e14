import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Cosmic Purple Theme - Engaging & Accessible Color Palette
  // Primary Colors - Deep cosmic backgrounds
  static const Color cosmicDeep = Color(0xFF0F0B1A); // Deep space background
  static const Color cosmicDark = Color(0xFF1A0B2E); // Primary dark background
  static const Color cosmicMedium = Color(0xFF16213E); // Secondary background
  static const Color cosmicSurface = Color(0xFF2D1B4E); // Card/surface color

  // Accent Colors - Vibrant purples for engagement
  static const Color purplePrimary = Color(0xFF8B5CF6); // Primary purple accent
  static const Color purpleSecondary = Color(
    0xFFA855F7,
  ); // Secondary purple accent
  static const Color purpleLight = Color(
    0xFFC084FC,
  ); // Light purple for highlights
  static const Color purpleDark = Color(0xFF7C3AED); // Dark purple for depth

  // Supporting Colors
  static const Color electricBlue = Color(0xFF3B82F6); // Blue highlights
  static const Color cosmicGold = Color(
    0xFFF59E0B,
  ); // Gold accents for premium features
  static const Color cosmicWhite = Color(0xFFFFFFFF); // Pure white text
  static const Color cosmicGray = Color(0xFFE5E7EB); // Light gray text
  static const Color cosmicMuted = Color(0xFF9CA3AF); // Muted text

  // Status Colors
  static const Color errorRed = Color(0xFFEF4444);
  static const Color warningOrange = Color(0xFFF59E0B);
  static const Color successGreen = Color(0xFF10B981);

  // Legacy color mappings for backward compatibility
  static const Color spotifyGreen = purplePrimary;
  static const Color spotifyBlack = cosmicDeep;
  static const Color spotifyDarkGrey = cosmicDark;
  static const Color spotifyGrey = cosmicSurface;
  static const Color spotifyLightGrey = cosmicMuted;
  static const Color spotifyWhite = cosmicWhite;
  static const Color spotifyOffWhite = cosmicGray;

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: purplePrimary,
      scaffoldBackgroundColor: cosmicDeep,
      colorScheme: const ColorScheme.dark(
        primary: purplePrimary,
        secondary: purpleSecondary,
        tertiary: electricBlue,
        surface: cosmicSurface,
        onPrimary: cosmicWhite,
        onSecondary: cosmicWhite,
        onSurface: cosmicWhite,
        onTertiary: cosmicWhite,
        error: errorRed,
        onError: cosmicWhite,
      ),

      // App Bar Theme with gradient background
      appBarTheme: AppBarTheme(
        backgroundColor: cosmicDeep,
        foregroundColor: cosmicWhite,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: cosmicWhite,
        ),
      ),

      // Text Theme with enhanced contrast
      textTheme: TextTheme(
        displayLarge: GoogleFonts.inter(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: cosmicWhite,
        ),
        displayMedium: GoogleFonts.inter(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: cosmicWhite,
        ),
        displaySmall: GoogleFonts.inter(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: cosmicWhite,
        ),
        headlineLarge: GoogleFonts.inter(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: cosmicWhite,
        ),
        headlineMedium: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: cosmicWhite,
        ),
        headlineSmall: GoogleFonts.inter(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: cosmicWhite,
        ),
        titleLarge: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: cosmicWhite,
        ),
        titleMedium: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: cosmicWhite,
        ),
        titleSmall: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: cosmicGray,
        ),
        bodyLarge: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.normal,
          color: cosmicWhite,
        ),
        bodyMedium: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: cosmicWhite,
        ),
        bodySmall: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.normal,
          color: cosmicGray,
        ),
        labelLarge: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: cosmicWhite,
        ),
        labelMedium: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: cosmicGray,
        ),
        labelSmall: GoogleFonts.inter(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: cosmicMuted,
        ),
      ),

      // Button Themes with gradient effects
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: purplePrimary,
          foregroundColor: cosmicWhite,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: purplePrimary,
          side: const BorderSide(color: purplePrimary),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: purplePrimary,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Input Decoration Theme with cosmic styling
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: cosmicSurface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: cosmicMuted, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: purplePrimary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorRed, width: 1),
        ),
        hintStyle: GoogleFonts.inter(color: cosmicMuted, fontSize: 14),
        labelStyle: GoogleFonts.inter(color: cosmicGray, fontSize: 14),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),

      // Card Theme with cosmic styling
      cardTheme: const CardThemeData(
        color: cosmicSurface,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: cosmicDark,
        selectedItemColor: purplePrimary,
        unselectedItemColor: cosmicMuted,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
      ),

      // Slider Theme with purple accents
      sliderTheme: const SliderThemeData(
        activeTrackColor: purplePrimary,
        inactiveTrackColor: cosmicMuted,
        thumbColor: purplePrimary,
        overlayColor: Color(0x298B5CF6), // Purple with transparency
        trackHeight: 4,
      ),

      // Icon Theme
      iconTheme: const IconThemeData(color: cosmicWhite, size: 24),

      // Divider Theme
      dividerTheme: const DividerThemeData(
        color: cosmicMuted,
        thickness: 1,
        space: 1,
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        titleTextStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: cosmicWhite,
        ),
        subtitleTextStyle: GoogleFonts.inter(fontSize: 14, color: cosmicGray),
        iconColor: cosmicGray,
      ),

      // Progress Indicator Theme
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: purplePrimary,
        linearTrackColor: cosmicMuted,
        circularTrackColor: cosmicMuted,
      ),
    );
  }

  // Custom text styles for specific use cases with cosmic theme
  static TextStyle get playlistTitle => GoogleFonts.inter(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: cosmicWhite,
  );

  static TextStyle get songTitle => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: cosmicWhite,
  );

  static TextStyle get artistName => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: cosmicGray,
  );

  static TextStyle get albumName => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: cosmicGray,
  );

  static TextStyle get duration => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: cosmicMuted,
  );

  static TextStyle get sectionHeader => GoogleFonts.inter(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: cosmicWhite,
  );

  static TextStyle get buttonText => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: cosmicWhite,
  );

  // Additional cosmic-themed text styles
  static TextStyle get accentText => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: purplePrimary,
  );

  static TextStyle get premiumText => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: cosmicGold,
  );

  // Gradient text helper (for use with ShaderMask)
  static const LinearGradient purpleGradient = LinearGradient(
    colors: [purplePrimary, purpleSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cosmicGradient = LinearGradient(
    colors: [cosmicDark, cosmicMedium],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
