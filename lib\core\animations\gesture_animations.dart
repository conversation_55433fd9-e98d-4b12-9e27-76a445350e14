import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';

/// Gesture-based animations for natural and responsive interactions
class CosmicGestureAnimations {
  static const Duration swipeDuration = Duration(milliseconds: 300);
  static const Duration tapDuration = Duration(milliseconds: 150);
}

/// Swipe-to-action animation with visual feedback
class CosmicSwipeAction extends StatefulWidget {
  final Widget child;
  final VoidCallback? onSwipeLeft;
  final VoidCallback? onSwipeRight;
  final double threshold;
  final Color leftActionColor;
  final Color rightActionColor;
  final IconData? leftIcon;
  final IconData? rightIcon;

  const CosmicSwipeAction({
    super.key,
    required this.child,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.threshold = 100.0,
    this.leftActionColor = AppTheme.errorRed,
    this.rightActionColor = AppTheme.successGreen,
    this.leftIcon = Icons.delete,
    this.rightIcon = Icons.favorite,
  });

  @override
  State<CosmicSwipeAction> createState() => _CosmicSwipeActionState();
}

class _CosmicSwipeActionState extends State<CosmicSwipeAction>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;
  double _dragDistance = 0.0;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: CosmicGestureAnimations.swipeDuration,
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    _isDragging = true;
    HapticFeedback.lightImpact();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    setState(() {
      _dragDistance += details.delta.dx;
      _dragDistance = _dragDistance.clamp(-200.0, 200.0);
    });

    final progress = (_dragDistance.abs() / widget.threshold).clamp(0.0, 1.0);
    _controller.value = progress;

    // Haptic feedback at threshold
    if (_dragDistance.abs() >= widget.threshold && !_controller.isCompleted) {
      HapticFeedback.mediumImpact();
    }
  }

  void _onPanEnd(DragEndDetails details) {
    _isDragging = false;
    
    if (_dragDistance.abs() >= widget.threshold) {
      // Action triggered
      HapticFeedback.heavyImpact();
      
      if (_dragDistance < 0 && widget.onSwipeLeft != null) {
        widget.onSwipeLeft!();
      } else if (_dragDistance > 0 && widget.onSwipeRight != null) {
        widget.onSwipeRight!();
      }
    }

    // Reset animation
    _controller.reverse();
    setState(() {
      _dragDistance = 0.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Stack(
            children: [
              // Background action indicators
              if (_dragDistance != 0.0) ...[
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: _dragDistance < 0 
                          ? widget.leftActionColor.withValues(alpha: _controller.value * 0.3)
                          : widget.rightActionColor.withValues(alpha: _controller.value * 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Align(
                      alignment: _dragDistance < 0 ? Alignment.centerLeft : Alignment.centerRight,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Icon(
                          _dragDistance < 0 ? widget.leftIcon : widget.rightIcon,
                          color: _dragDistance < 0 ? widget.leftActionColor : widget.rightActionColor,
                          size: 24 + (_controller.value * 8),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
              // Main content
              Transform.translate(
                offset: Offset(_dragDistance * 0.3, 0),
                child: Transform.scale(
                  scale: _scaleAnimation.value,
                  child: widget.child,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

/// Long press animation with ripple effect
class CosmicLongPressAnimation extends StatefulWidget {
  final Widget child;
  final VoidCallback? onLongPress;
  final Duration duration;
  final Color rippleColor;

  const CosmicLongPressAnimation({
    super.key,
    required this.child,
    this.onLongPress,
    this.duration = const Duration(milliseconds: 800),
    this.rippleColor = AppTheme.purplePrimary,
  });

  @override
  State<CosmicLongPressAnimation> createState() => _CosmicLongPressAnimationState();
}

class _CosmicLongPressAnimationState extends State<CosmicLongPressAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rippleAnimation;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed && _isPressed) {
        HapticFeedback.heavyImpact();
        widget.onLongPress?.call();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _isPressed = true;
    _controller.forward();
    HapticFeedback.lightImpact();
  }

  void _onTapUp(TapUpDetails details) {
    _isPressed = false;
    _controller.reverse();
  }

  void _onTapCancel() {
    _isPressed = false;
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              // Ripple effect
              if (_rippleAnimation.value > 0) ...[
                Container(
                  width: 100 * _rippleAnimation.value,
                  height: 100 * _rippleAnimation.value,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.rippleColor.withValues(
                      alpha: (1.0 - _rippleAnimation.value) * 0.3,
                    ),
                  ),
                ),
              ],
              // Main content
              Transform.scale(
                scale: _scaleAnimation.value,
                child: widget.child,
              ),
            ],
          );
        },
      ),
    );
  }
}

/// Pull-to-refresh animation with cosmic effects
class CosmicPullToRefresh extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double triggerDistance;

  const CosmicPullToRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.triggerDistance = 100.0,
  });

  @override
  State<CosmicPullToRefresh> createState() => _CosmicPullToRefreshState();
}

class _CosmicPullToRefreshState extends State<CosmicPullToRefresh>
    with TickerProviderStateMixin {
  late AnimationController _pullController;
  late AnimationController _refreshController;
  late Animation<double> _pullAnimation;
  late Animation<double> _rotationAnimation;
  
  double _pullDistance = 0.0;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _pullController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _refreshController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pullAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pullController,
      curve: Curves.easeOut,
    ));
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 6.28, // 2π radians
    ).animate(_refreshController);
  }

  @override
  void dispose() {
    _pullController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  bool _onNotification(ScrollNotification notification) {
    if (notification is ScrollUpdateNotification && !_isRefreshing) {
      if (notification.metrics.pixels < 0) {
        setState(() {
          _pullDistance = (-notification.metrics.pixels).clamp(0.0, widget.triggerDistance * 1.5);
        });
        
        final progress = (_pullDistance / widget.triggerDistance).clamp(0.0, 1.0);
        _pullController.value = progress;

        if (_pullDistance >= widget.triggerDistance) {
          HapticFeedback.mediumImpact();
        }
      }
    } else if (notification is ScrollEndNotification && !_isRefreshing) {
      if (_pullDistance >= widget.triggerDistance) {
        _triggerRefresh();
      } else {
        _resetPull();
      }
    }
    return false;
  }

  void _triggerRefresh() async {
    setState(() {
      _isRefreshing = true;
    });
    
    _refreshController.repeat();
    HapticFeedback.heavyImpact();
    
    try {
      await widget.onRefresh();
    } finally {
      _refreshController.stop();
      _resetPull();
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  void _resetPull() {
    _pullController.reverse();
    setState(() {
      _pullDistance = 0.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: _onNotification,
      child: Stack(
        children: [
          widget.child,
          // Pull indicator
          if (_pullDistance > 0 || _isRefreshing) ...[
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: AnimatedBuilder(
                animation: _pullAnimation,
                builder: (context, child) {
                  return Container(
                    height: _pullDistance.clamp(0.0, 80.0),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppTheme.purplePrimary.withValues(alpha: _pullAnimation.value * 0.3),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: Center(
                      child: AnimatedBuilder(
                        animation: _rotationAnimation,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _isRefreshing ? _rotationAnimation.value : 0,
                            child: Icon(
                              Icons.refresh,
                              color: AppTheme.purplePrimary,
                              size: 24 + (_pullAnimation.value * 8),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }
}
